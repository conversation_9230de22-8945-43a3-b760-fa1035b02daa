<template>
  <view class="container">
    <view class="detail-content">
      <view class="detail-header">
        <text class="detail-title">{{detailData.title || detailData.name || '详情'}}</text>
        <view class="detail-status" :class="'status-' + detailData.status">
          {{getStatusText(detailData.status)}}
        </view>
      </view>

      <view class="detail-info">
        <view class="info-item" v-if="detailData.description">
          <text class="info-label">描述：</text>
          <text class="info-value">{{detailData.description}}</text>
        </view>
        <view class="info-item" v-if="detailData.phone">
          <text class="info-label">电话：</text>
          <text class="info-value">{{detailData.phone}}</text>
        </view>
        <view class="info-item" v-if="detailData.employeeName">
          <text class="info-label">负责人：</text>
          <text class="info-value">{{detailData.employeeName}}</text>
        </view>
        <view class="info-item" v-if="detailData.createTime">
          <text class="info-label">创建时间：</text>
          <text class="info-value">{{detailData.createTime}}</text>
        </view>
      </view>

      <view class="detail-actions">
        <view class="action-btn primary-btn" @tap="handleEdit">
          <text class="btn-text">编辑</text>
        </view>
        <view class="action-btn secondary-btn" @tap="goBack">
          <text class="btn-text">返回</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'YunqiDetail',
  data() {
    return {
      detailData: {},
      itemId: '',
      itemType: ''
    }
  },
  onLoad(options) {
    console.log('详情页参数:', options)
    this.itemId = options.id || ''
    this.itemType = options.type || ''
    this.loadDetailData()
  },
  methods: {
    // 加载详情数据
    loadDetailData() {
      // 模拟数据，实际应该根据 id 和 type 调用 API
      const mockData = {
        myTodo: {
          id: this.itemId,
          title: '客户回访详情',
          description: '联系张先生确认产品需求，了解具体的智能家居配置要求',
          createTime: '2024-01-15 10:30',
          status: 'pending'
        },
        employeeTodo: {
          id: this.itemId,
          title: '产品演示详情',
          description: '为王先生演示智能家居系统的完整功能',
          createTime: '2024-01-15 14:00',
          employeeName: '小李',
          status: 'pending'
        },
        myCustomer: {
          id: this.itemId,
          name: '张先生',
          phone: '138****1234',
          createTime: '2024-01-10 09:15',
          status: 'active'
        },
        employeeCustomer: {
          id: this.itemId,
          name: '王先生',
          phone: '137****9012',
          createTime: '2024-01-13 15:45',
          employeeName: '小李',
          status: 'active'
        }
      }

      this.detailData = mockData[this.itemType] || {}
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待处理',
        'processing': '处理中',
        'completed': '已完成',
        'cancelled': '已取消',
        'active': '活跃客户',
        'potential': '潜在客户',
        'inactive': '非活跃',
        'lost': '流失客户'
      }
      return statusMap[status] || '未知'
    },

    // 编辑
    handleEdit() {
      uni.showToast({
        title: '跳转到编辑页面',
        icon: 'none'
      })
    },

    // 返回
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

.detail-content {
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
}

.detail-header {
  padding: 40rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
}

.detail-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #ffffff;

  &.status-pending {
    background-color: #ff9500;
  }

  &.status-processing {
    background-color: #007aff;
  }

  &.status-completed {
    background-color: #34c759;
  }

  &.status-cancelled {
    background-color: #ff3b30;
  }

  &.status-active {
    background-color: #34c759;
  }

  &.status-potential {
    background-color: #007aff;
  }

  &.status-inactive {
    background-color: #8e8e93;
  }

  &.status-lost {
    background-color: #ff3b30;
  }
}

.detail-info {
  padding: 30rpx;
}

.info-item {
  display: flex;
  margin-bottom: 30rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: 28rpx;
  color: #666666;
  min-width: 140rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
}

.detail-actions {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 8rpx;
  text-align: center;

  &:active {
    opacity: 0.8;
  }
}

.primary-btn {
  background-color: #007aff;
}

.secondary-btn {
  background-color: #8e8e93;
}

.btn-text {
  color: #ffffff;
  font-size: 28rpx;
}
</style>