<template>
	<view class="container">
		<!-- Tab 切换 -->
		<tn-sticky>
			<view class="tab-header">
				<tn-tabs :list="tabList" :current="currentTab" :isScroll="false" name="name" @change="tabChange"
					activeColor="#01BEFF" inactiveColor="#666666" :bold="true" backgroundColor="#ffffff"
					:bar-width="100" :fontSize="32"></tn-tabs>
			</view>
		</tn-sticky>

		<!-- 列表内容 -->
		<view class="list-content">
			<!-- 我的代办 -->
			<view v-if="currentTab === 0" class="tab-content">
				<view class="list-header">
					<text class="list-title">我的代办</text>
					<text class="list-count">共 {{myTodoList.length}} 条</text>
				</view>
				<view class="list-wrapper">
					<view v-for="(item, index) in myTodoList" :key="index" class="list-item"
						@tap="handleItemClick(item, 'myTodo')">
						<view class="item-content">
							<view class="item-title">{{item.title}}</view>
							<view class="item-desc">{{item.description}}</view>
							<view class="item-info">
								<text class="item-time">{{item.createTime}}</text>
								<view class="item-status" :class="'status-' + item.status">
									{{getStatusText(item.status)}}
								</view>
							</view>
						</view>
						<view class="item-arrow">
							<text class="tn-icon-right"></text>
						</view>
					</view>
					<view v-if="myTodoList.length === 0" class="empty-state">
						<text class="empty-text">暂无代办事项</text>
					</view>
				</view>
			</view>

			<!-- 员工代办 -->
			<view v-if="currentTab === 1" class="tab-content">
				<view class="list-header">
					<text class="list-title">员工代办</text>
					<text class="list-count">共 {{employeeTodoList.length}} 条</text>
				</view>
				<view class="list-wrapper">
					<view v-for="(item, index) in employeeTodoList" :key="index" class="list-item"
						@tap="handleItemClick(item, 'employeeTodo')">
						<view class="item-content">
							<view class="item-title">{{item.title}}</view>
							<view class="item-desc">{{item.description}}</view>
							<view class="item-info">
								<text class="item-time">{{item.createTime}}</text>
								<text class="item-employee">负责人：{{item.employeeName}}</text>
								<view class="item-status" :class="'status-' + item.status">
									{{getStatusText(item.status)}}
								</view>
							</view>
						</view>
						<view class="item-arrow">
							<text class="tn-icon-right"></text>
						</view>
					</view>
					<view v-if="employeeTodoList.length === 0" class="empty-state">
						<text class="empty-text">暂无员工代办事项</text>
					</view>
				</view>
			</view>

			<!-- 我的客户 -->
			<view v-if="currentTab === 2" class="tab-content">
				<view class="list-header">
					<text class="list-title">我的客户</text>
					<text class="list-count">共 {{myCustomerList.length}} 条</text>
				</view>
				<view class="list-wrapper">
					<view v-for="(item, index) in myCustomerList" :key="index" class="list-item"
						@tap="handleItemClick(item, 'myCustomer')">
						<view class="item-content">
							<view class="item-title">{{item.name}}</view>
							<view class="item-desc">{{item.phone}}</view>
							<view class="item-info">
								<text class="item-time">{{item.createTime}}</text>
								<view class="item-status" :class="'status-' + item.status">
									{{getCustomerStatusText(item.status)}}
								</view>
							</view>
						</view>
						<view class="item-arrow">
							<text class="tn-icon-right"></text>
						</view>
					</view>
					<view v-if="myCustomerList.length === 0" class="empty-state">
						<text class="empty-text">暂无客户信息</text>
					</view>
				</view>
			</view>

			<!-- 员工客户 -->
			<view v-if="currentTab === 3" class="tab-content">
				<view class="list-header">
					<text class="list-title">员工客户</text>
					<text class="list-count">共 {{employeeCustomerList.length}} 条</text>
				</view>
				<view class="list-wrapper">
					<view v-for="(item, index) in employeeCustomerList" :key="index" class="list-item"
						@tap="handleItemClick(item, 'employeeCustomer')">
						<view class="item-content">
							<view class="item-title">{{item.name}}</view>
							<view class="item-desc">{{item.phone}}</view>
							<view class="item-info">
								<text class="item-time">{{item.createTime}}</text>
								<text class="item-employee">负责人：{{item.employeeName}}</text>
								<view class="item-status" :class="'status-' + item.status">
									{{getCustomerStatusText(item.status)}}
								</view>
							</view>
						</view>
						<view class="item-arrow">
							<text class="tn-icon-right"></text>
						</view>
					</view>
					<view v-if="employeeCustomerList.length === 0" class="empty-state">
						<text class="empty-text">暂无员工客户信息</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'ListIndex',
		data() {
			return {
				// 当前选中的tab索引
				currentTab: 0,
				// tab列表配置
				tabList: [{
						name: '我的代办'
					},
					{
						name: '员工代办'
					},
					{
						name: '我的客户'
					},
					{
						name: '员工客户'
					}
				],
				// 我的代办列表
				myTodoList: [],
				// 员工代办列表
				employeeTodoList: [],
				// 我的客户列表
				myCustomerList: [],
				// 员工客户列表
				employeeCustomerList: []
			}
		},
		onLoad() {
			this.loadData()
		},
		methods: {
			// tab切换事件
			tabChange(index) {
				this.currentTab = index
				this.loadData()
			},

			// 加载数据
			loadData() {
				switch (this.currentTab) {
					case 0:
						this.loadMyTodoList()
						break
					case 1:
						this.loadEmployeeTodoList()
						break
					case 2:
						this.loadMyCustomerList()
						break
					case 3:
						this.loadEmployeeCustomerList()
						break
				}
			},

			// 加载我的代办数据
			loadMyTodoList() {
				// 模拟数据，实际应该调用API
				this.myTodoList = [{
						id: 1,
						title: '客户回访',
						description: '联系张先生确认产品需求',
						createTime: '2024-01-15 10:30',
						status: 'pending'
					},
					{
						id: 2,
						title: '合同审核',
						description: '审核李女士的购买合同',
						createTime: '2024-01-14 16:20',
						status: 'processing'
					}
				]
			},

			// 加载员工代办数据
			loadEmployeeTodoList() {
				// 模拟数据，实际应该调用API
				this.employeeTodoList = [{
					id: 1,
					title: '产品演示',
					description: '为王先生演示智能家居系统',
					createTime: '2024-01-15 14:00',
					employeeName: '小李',
					status: 'pending'
				}]
			},

			// 加载我的客户数据
			loadMyCustomerList() {
				// 模拟数据，实际应该调用API
				this.myCustomerList = [{
						id: 1,
						name: '张先生',
						phone: '138****1234',
						createTime: '2024-01-10 09:15',
						status: 'active'
					},
					{
						id: 2,
						name: '李女士',
						phone: '139****5678',
						createTime: '2024-01-12 11:30',
						status: 'potential'
					}
				]
			},

			// 加载员工客户数据
			loadEmployeeCustomerList() {
				// 模拟数据，实际应该调用API
				this.employeeCustomerList = [{
					id: 1,
					name: '王先生',
					phone: '137****9012',
					createTime: '2024-01-13 15:45',
					employeeName: '小李',
					status: 'active'
				}]
			},

			// 获取状态文本
			getStatusText(status) {
				const statusMap = {
					'pending': '待处理',
					'processing': '处理中',
					'completed': '已完成',
					'cancelled': '已取消'
				}
				return statusMap[status] || '未知'
			},

			// 获取客户状态文本
			getCustomerStatusText(status) {
				const statusMap = {
					'active': '活跃客户',
					'potential': '潜在客户',
					'inactive': '非活跃',
					'lost': '流失客户'
				}
				return statusMap[status] || '未知'
			},

			// 列表项点击事件
			handleItemClick(item, type) {
				console.log('点击了项目:', item, '类型:', type)
				// 这里可以根据不同类型跳转到不同的详情页面
				// uni.navigateTo({
				//   url: `/pages/detail/index?id=${item.id}&type=${type}`
				// })
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.tab-header {
		background-color: #ffffff;
		border-bottom: 1rpx solid #e5e5e5;
	}

	.list-content {
		padding: 20rpx;
	}

	.tab-content {
		background-color: #ffffff;
		border-radius: 12rpx;
		overflow: hidden;
	}

	.list-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		background-color: #fafafa;
	}

	.list-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.list-count {
		font-size: 28rpx;
		color: #666666;
	}

	.list-wrapper {
		padding: 0;
	}

	.list-item {
		display: flex;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		background-color: #ffffff;

		&:last-child {
			border-bottom: none;
		}

		&:active {
			background-color: #f8f8f8;
		}
	}

	.item-content {
		flex: 1;
	}

	.item-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333333;
		margin-bottom: 10rpx;
	}

	.item-desc {
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 15rpx;
	}

	.item-info {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.item-time {
		font-size: 24rpx;
		color: #999999;
	}

	.item-employee {
		font-size: 24rpx;
		color: #666666;
	}

	.item-status {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 22rpx;
		color: #ffffff;

		&.status-pending {
			background-color: #ff9500;
		}

		&.status-processing {
			background-color: #007aff;
		}

		&.status-completed {
			background-color: #34c759;
		}

		&.status-cancelled {
			background-color: #ff3b30;
		}

		&.status-active {
			background-color: #34c759;
		}

		&.status-potential {
			background-color: #007aff;
		}

		&.status-inactive {
			background-color: #8e8e93;
		}

		&.status-lost {
			background-color: #ff3b30;
		}
	}

	.item-arrow {
		margin-left: 20rpx;
		color: #c7c7cc;
		font-size: 28rpx;
	}

	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 100rpx 30rpx;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}
</style>