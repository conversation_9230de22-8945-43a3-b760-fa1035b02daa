<template>
  <view class="container">
    <view class="detail-content">
      <text class="title">云栖流程详情</text>
      <text class="desc">详情页面正在开发中...</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DetailIndex',
  data() {
    return {
      
    }
  },
  onLoad(options) {
    console.log('详情页参数:', options)
  },
  methods: {
    
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

.detail-content {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 60rpx 40rpx;
  text-align: center;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  display: block;
  margin-bottom: 30rpx;
}

.desc {
  font-size: 28rpx;
  color: #666666;
  display: block;
}
</style>
